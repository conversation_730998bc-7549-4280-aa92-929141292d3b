
> speakmcp@0.0.3 dev
> electron-vite dev --watch

vite v5.4.8 building SSR bundle for development...

watching for file changes...

build started...
transforming...
✓ 385 modules transformed.
rendering chunks...
out/main/index.js             176.60 kB
out/main/updater-l5s32Xwz.js  472.98 kB
built in 754ms.

build the electron main process successfully

-----

vite v5.4.8 building SSR bundle for development...

watching for file changes...

build started...
transforming...
✓ 2 modules transformed.
rendering chunks...
out/preload/index.mjs  2.31 kB
built in 9ms.

build the electron preload files successfully

-----

dev server running for the electron renderer process at:

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose

start electron app...

Skip checkForUpdates because application is not packed and dev update config is not forced
Skip checkForUpdates because application is not packed and dev update config is not forced
[PANEL-DEBUG] 📍 Showing panel window: {
  mode: 'normal',
  isTextInputActive: false,
  currentBounds: { x: 1082, y: 45, width: 260, height: 50 },
  targetPosition: { x: 1082, y: 45 }
}
Skip checkForUpdates because application is not packed and dev update config is not forced
[MCP-AGENT-DEBUG] 📞 TIPC call: resizePanelToNormal
[MCP-AGENT-DEBUG] 📏 Attempting to resize panel to normal...
[MCP-AGENT-DEBUG] ✅ Panel resized to normal: {
  newSize: { width: 260, height: 50 },
  newPosition: { x: 1082, y: 45 },
  finalBounds: { x: 1082, y: 45, width: 260, height: 50 }
}
[MCP-SERVICE] 🚀 Initializing server: Headless Terminal
[MCP-SERVICE] ✅ Connected to server: Headless Terminal
[MCP-SERVICE] 📋 Found 6 tools from Headless Terminal
[MCP-SERVICE] ✅ Successfully initialized server: Headless Terminal
Skip checkForUpdates because application is not packed and dev update config is not forced
[MCP-CONVERSATION-DEBUG] 🆕 Creating new conversation for voice input
[CONVERSATION] Saved conversation conv_1753386413195_toehklgyk
[MCP-CONVERSATION-DEBUG] ✅ Created conversation: conv_1753386413195_toehklgyk
[MCP-AGENT] 🤖 Agent mode enabled, using agent processing...
[MCP-RECORDING-DEBUG] 📝 Transcript: " Which process is using the most CPU?..."
[MCP-RECORDING-DEBUG] 🆔 ConversationId from input: undefined
[MCP-RECORDING-DEBUG] 🆔 Using conversationId: conv_1753386413195_toehklgyk
[UNIFIED-AGENT-DEBUG] 🚀 processWithAgentMode called
[UNIFIED-AGENT-DEBUG] 📝 Text: " Which process is using the most CPU?..."
[UNIFIED-AGENT-DEBUG] 🆔 ConversationId: conv_1753386413195_toehklgyk
[MCP-SERVICE] 🚀 Initializing server: Headless Terminal
[MCP-SERVICE] ✅ Connected to server: Headless Terminal
[MCP-SERVICE] 📋 Found 6 tools from Headless Terminal
[MCP-SERVICE] ✅ Successfully initialized server: Headless Terminal
[UNIFIED-AGENT-DEBUG] 🔍 Checking conversation context...
[UNIFIED-AGENT-DEBUG] 📂 Loading conversation: conv_1753386413195_toehklgyk
[CONVERSATION] Loaded conversation conv_1753386413195_toehklgyk
[UNIFIED-AGENT-DEBUG] 📋 Loaded conversation: 1 messages
[UNIFIED-AGENT-DEBUG] ✅ Converted 0 messages for agent mode
[MCP-AGENT] 📚 Loaded 0 previous messages from conversation conv_1753386413195_toehklgyk
[MCP-AGENT] 🤖 Starting agent mode processing...
[PANEL-DEBUG] 📍 Showing panel window: {
  mode: 'normal',
  isTextInputActive: false,
  currentBounds: { x: 1082, y: 45, width: 260, height: 50 },
  targetPosition: { x: 1082, y: 45 }
}
[MCP-AGENT-DEBUG] 🔧 Available tools: Headless Terminal:ht_create_session, Headless Terminal:ht_send_keys, Headless Terminal:ht_take_snapshot, Headless Terminal:ht_execute_command, Headless Terminal:ht_list_sessions, Headless Terminal:ht_close_session, Headless Terminal:ht_create_session, Headless Terminal:ht_send_keys, Headless Terminal:ht_take_snapshot, Headless Terminal:ht_execute_command, Headless Terminal:ht_list_sessions, Headless Terminal:ht_close_session
[MCP-AGENT-DEBUG] 🎯 Tool capabilities: Detected system capabilities. Can help with this request using available tools.
[MCP-AGENT-DEBUG] 📚 Loaded 0 previous messages from conversation
[MCP-AGENT-DEBUG] 📋 Using 1 recent messages for context
[MCP-AGENT] 🔄 Agent iteration 1/10
[MCP-AGENT-DEBUG] 🧠 Making LLM call for iteration 1
[MCP-LLM-DEBUG] 🚀 Using structured output for agent mode
[STRUCTURED-OUTPUT] Using model: moonshotai/kimi-k2-instruct
[STRUCTURED-OUTPUT] Provider: groq
[STRUCTURED-OUTPUT] Falling back to regular completion for moonshotai/kimi-k2-instruct
[MCP-AGENT-DEBUG] 📞 TIPC call: resizePanelForAgentMode
[MCP-AGENT-DEBUG] ✅ Panel resized for agent mode: {
  newSize: { width: 420, height: 240 },
  newPosition: { x: 922, y: 45 },
  finalBounds: { x: 922, y: 45, width: 420, height: 240 }
}
[MCP-LLM-DEBUG] ✅ Structured output successful: {
  "toolCalls": [
    {
      "name": "ht_create_session",
      "arguments": {
        "command": [
          "top",
          "-l",
          "1",
          "-n",
          "10",
          "-o",
          "cpu"
        ]
      }
    }
  ],
  "content": "Creating a terminal session to run top command and identify the process using the most CPU",
  "needsMoreWork": true
}
[MCP-AGENT-DEBUG] 🎯 LLM response for iteration 1: {
  "toolCalls": [
    {
      "name": "ht_create_session",
      "arguments": {
        "command": [
          "top",
          "-l",
          "1",
          "-n",
          "10",
          "-o",
          "cpu"
        ]
      }
    }
  ],
  "content": "Creating a terminal session to run top command and identify the process using the most CPU",
  "needsMoreWork": true
}
[MCP-AGENT] 🔧 Executing 1 tool calls
[MCP-AGENT] Executing tool: ht_create_session
[MCP-SERVICE] 🔧 Found matching tool with prefix: Headless Terminal:ht_create_session for unprefixed call: ht_create_session
[MCP-TOOL] 🔧 Executing ht_create_session with arguments: {
  command: [
    'top', '-l',
    '1',   '-n',
    '10',  '-o',
    'cpu'
  ]
}
[MCP-RESOURCE] 📝 Tracking session f88618df-2432-4c38-a9a7-20395b0588f2 for server Headless Terminal
[MCP-RESOURCE] 🎯 Auto-detected session f88618df-2432-4c38-a9a7-20395b0588f2 for server Headless Terminal
[MCP-AGENT] 🔄 Agent iteration 2/10
[MCP-AGENT-DEBUG] 🧠 Making LLM call for iteration 2
[MCP-LLM-DEBUG] 🚀 Using structured output for agent mode
[STRUCTURED-OUTPUT] Using model: moonshotai/kimi-k2-instruct
[STRUCTURED-OUTPUT] Provider: groq
[STRUCTURED-OUTPUT] Falling back to regular completion for moonshotai/kimi-k2-instruct
[MCP-LLM-DEBUG] ✅ Structured output successful: {
  "toolCalls": [
    {
      "name": "ht_execute_command",
      "arguments": {
        "command": "ps -eo pid,ppid,cmd,%cpu,%mem --sort=-%cpu | head -10",
        "sessionId": "f88618df-2432-4c38-a9a7-20395b0588f2"
      }
    }
  ],
  "content": "Running commands to identify the process using the most CPU",
  "needsMoreWork": true
}
[MCP-AGENT-DEBUG] 🎯 LLM response for iteration 2: {
  "toolCalls": [
    {
      "name": "ht_execute_command",
      "arguments": {
        "command": "ps -eo pid,ppid,cmd,%cpu,%mem --sort=-%cpu | head -10",
        "sessionId": "f88618df-2432-4c38-a9a7-20395b0588f2"
      }
    }
  ],
  "content": "Running commands to identify the process using the most CPU",
  "needsMoreWork": true
}
[MCP-AGENT] 🔧 Executing 1 tool calls
[MCP-AGENT] Executing tool: ht_execute_command
[MCP-SERVICE] 🔧 Found matching tool with prefix: Headless Terminal:ht_execute_command for unprefixed call: ht_execute_command
[MCP-TOOL] 🔧 Executing ht_execute_command with arguments: {
  command: 'ps -eo pid,ppid,cmd,%cpu,%mem --sort=-%cpu | head -10',
  sessionId: 'f88618df-2432-4c38-a9a7-20395b0588f2'
}
[MCP-AGENT] ⚠️ Tool execution had errors: ht_execute_command
[MCP-AGENT] 🔄 Agent iteration 3/10
[MCP-AGENT-DEBUG] 🧠 Making LLM call for iteration 3
[MCP-LLM-DEBUG] 🚀 Using structured output for agent mode
[STRUCTURED-OUTPUT] Using model: moonshotai/kimi-k2-instruct
[STRUCTURED-OUTPUT] Provider: groq
[STRUCTURED-OUTPUT] Falling back to regular completion for moonshotai/kimi-k2-instruct
[MCP-LLM-DEBUG] ✅ Structured output successful: {
  "toolCalls": [
    {
      "name": "ht_create_session",
      "arguments": {
        "command": [
          "top",
          "-l",
          "1",
          "-n",
          "10",
          "-o",
          "cpu"
        ]
      }
    }
  ],
  "content": "Creating a new terminal session to run top command and identify the process using the most CPU",
  "needsMoreWork": true
}
[MCP-AGENT-DEBUG] 🎯 LLM response for iteration 3: {
  "toolCalls": [
    {
      "name": "ht_create_session",
      "arguments": {
        "command": [
          "top",
          "-l",
          "1",
          "-n",
          "10",
          "-o",
          "cpu"
        ]
      }
    }
  ],
  "content": "Creating a new terminal session to run top command and identify the process using the most CPU",
  "needsMoreWork": true
}
[MCP-AGENT] 🔧 Executing 1 tool calls
[MCP-AGENT] Executing tool: ht_create_session
[MCP-SERVICE] 🔧 Found matching tool with prefix: Headless Terminal:ht_create_session for unprefixed call: ht_create_session
[MCP-TOOL] 🔧 Executing ht_create_session with arguments: {
  command: [
    'top', '-l',
    '1',   '-n',
    '10',  '-o',
    'cpu'
  ]
}
[MCP-RESOURCE] 📝 Tracking session 09167176-1483-4835-86b0-762a92e9eb0a for server Headless Terminal
[MCP-RESOURCE] 🎯 Auto-detected session 09167176-1483-4835-86b0-762a92e9eb0a for server Headless Terminal
[MCP-AGENT] 🔄 Agent iteration 4/10
[MCP-AGENT-DEBUG] 🧠 Making LLM call for iteration 4
[MCP-LLM-DEBUG] 🚀 Using structured output for agent mode
[STRUCTURED-OUTPUT] Using model: moonshotai/kimi-k2-instruct
[STRUCTURED-OUTPUT] Provider: groq
[STRUCTURED-OUTPUT] Falling back to regular completion for moonshotai/kimi-k2-instruct
[STRUCTURED-OUTPUT] ⚠️ JSON parsing failed, returning as content
[MCP-LLM-DEBUG] ✅ Structured output successful: {
  "content": "Running top command to identify the process using the most CPU"
}
[MCP-AGENT-DEBUG] 🎯 LLM response for iteration 4: {
  "content": "Running top command to identify the process using the most CPU"
}
[MCP-AGENT] ✅ Agent completed task in 4 iterations
[MCP-AGENT] ✅ Agent processing completed in 4 iterations
[MCP-AGENT] Final response length: 62
[MCP-AGENT] Final response preview: "Running top command to identify the process using the most CPU..."
[MCP-AGENT] Conversation history length: 9 entries
[MCP-AGENT] Final response will be displayed in GUI
[CONVERSATION] Loaded conversation conv_1753386413195_toehklgyk
[CONVERSATION] Saved conversation conv_1753386413195_toehklgyk
[MCP-CONVERSATION-DEBUG] ✅ Added assistant response to conversation
[MCP-AGENT] ✅ Agent processing completed, result displayed in GUI
[MCP-AGENT] 📋 Result will remain visible until user presses ESC to close
[CONVERSATION] Loaded conversation conv_1753386413195_toehklgyk
Skip checkForUpdates because application is not packed and dev update config is not forced
close panel
close main
